import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GameService } from '../../../core/services/game.service';
import { CartService } from '../../../core/services/cart.service';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { Game } from '../../../core/models/game.model';
import { Cart } from '../../../core/models/cart.model';

@Component({
  selector: 'app-profile-game-detail',
  standalone: false,
  templateUrl: './profile-game-detail.component.html',
  styleUrls: ['./profile-game-detail.component.css']
})
export class ProfileGameDetailComponent implements OnInit, OnDestroy {
  gameId: number | null = null;

  game: Game | null = null;
  loading = false;
  error = '';

  // Gallery
  showGalleryModal = false;
  currentImageIndex = 0;
  galleryImages: string[] = [];
  private keydownListener?: (event: KeyboardEvent) => void;

  // Cart
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  private cartSubscription?: Subscription;
  private cartChangeSubscription?: Subscription;

  constructor(
    private gameService: GameService,
    private cartService: CartService,
    private authService: AuthService,
    private modalService: ModalService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Get game ID from route parameters
    this.route.params.subscribe(params => {
      this.gameId = +params['id'];
      if (this.gameId) {
        this.loadGame();
      }
    });
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    if (this.cartSubscription) {
      this.cartSubscription.unsubscribe();
    }
    if (this.cartChangeSubscription) {
      this.cartChangeSubscription.unsubscribe();
    }
    this.removeKeydownListener();
  }

  private setupCartSubscription(): void {
    // Subscribe to cart data
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cart = cart;
    });

    // Subscribe to cart changes to update game states
    this.cartChangeSubscription = this.cartService.cartChanges$.subscribe(change => {
      // Update the current game's is_in_cart status if it matches
      if (this.game && this.game.id === change.gameId) {
        this.game.is_in_cart = change.action === 'added';
      }
    });
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.setupGallery();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить информацию об игре';
        this.loading = false;
      }
    });
  }

  private setupGallery(): void {
    if (this.game?.gallery_items) {
      this.galleryImages = this.game.gallery_items.map(item => item.file);
    }
  }

  // Gallery methods
  openGallery(index: number = 0): void {
    if (this.galleryImages.length === 0) return;
    
    this.currentImageIndex = index;
    this.showGalleryModal = true;
    this.addKeydownListener();
  }

  closeGallery(): void {
    this.showGalleryModal = false;
    this.removeKeydownListener();
  }

  nextImage(): void {
    if (this.currentImageIndex < this.galleryImages.length - 1) {
      this.currentImageIndex++;
    }
  }

  prevImage(): void {
    if (this.currentImageIndex > 0) {
      this.currentImageIndex--;
    }
  }

  private addKeydownListener(): void {
    this.keydownListener = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        this.prevImage();
      } else if (event.key === 'ArrowRight') {
        this.nextImage();
      } else if (event.key === 'Escape') {
        this.closeGallery();
      }
    };
    document.addEventListener('keydown', this.keydownListener);
  }

  private removeKeydownListener(): void {
    if (this.keydownListener) {
      document.removeEventListener('keydown', this.keydownListener);
      this.keydownListener = undefined;
    }
  }

  // Game status methods
  isInCart(): boolean {
    return this.game?.is_in_cart || false;
  }

  isInLibrary(): boolean {
    return this.game?.is_in_library || false;
  }

  hasAccess(): boolean {
    return this.game?.has_access || false;
  }

  canPlay(): boolean {
    return this.isInLibrary() && this.hasAccess();
  }

  canBuy(): boolean {
    return !this.isInLibrary() && !this.isInCart();
  }

  needsAccessExtension(): boolean {
    return this.isInLibrary() && !this.hasAccess();
  }

  // Cart actions
  addToCart(): void {
    if (!this.game) return;

    if (this.isInLibrary()) {
      this.modalService.error('Игра уже в библиотеке', 'Эта игра уже есть в вашей библиотеке');
      return;
    }

    this.cartService.addToCart(this.game).subscribe({
      next: () => {
        if (this.game) {
          this.game.is_in_cart = true;
        }
        console.log('Game added to cart successfully');
      },
      error: (error) => {
        console.error('Error adding game to cart:', error.message);
        this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
      }
    });
  }

  closeModal(): void {
    this.router.navigate(['/profile/games']);
  }

  // Utility methods
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatPrice(price: string): string {
    const num = parseFloat(price);
    return num.toLocaleString('ru-RU', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }
}
