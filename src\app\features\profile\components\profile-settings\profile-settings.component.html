<!-- Profile Settings Content -->
<div class="max-w-6xl">

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-20">
    <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-6">
      <svg class="animate-spin h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="errorMessage && !isLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center mb-6">
    <p class="text-red-300 mb-4">{{ errorMessage }}</p>
    <button
      (click)="loadUserProfile()"
      class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Profile Content -->
  <div *ngIf="userProfile && !isLoading && !errorMessage">
  <!-- Header with User Icon and Title -->
  <div class="flex items-center mb-8">
    <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6">
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-4xl font-bold text-white mb-2">Параметры</h1>
      <p class="text-gray-300">Управляйте данными своей учетной записи</p>
    </div>
  </div>

  <!-- Account Information Section -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-8 mb-8 profile-section">
    <h3 class="text-lg font-semibold text-white mb-6">Информация об аккаунте</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Email Field -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
        <div class="relative">
          <input type="email" [value]="userProfile?.email" readonly class="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Password Field -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Пароль</label>
        <div class="relative">
          <input type="password" value="••••••••" readonly class="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Phone Field -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Телефон</label>
        <div class="relative">
          <input type="tel" placeholder="+7 (___) ___-__-__" class="w-full px-4 py-3 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-4 mt-8">
      <button class="px-6 py-3 bg-gradient-to-r from-blue-600 to-slate-700 text-white font-medium rounded-lg shadow-lg hover:from-blue-700 hover:to-slate-800 transition-all transform hover:scale-[1.02] profile-button">
        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Обновить профиль
      </button>
      <button (click)="onRefreshProfile()" class="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-[1.02] profile-button">
        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Обновить данные
      </button>
    </div>
  </div>

  <!-- Account Status Section -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 profile-section">
    <h3 class="text-lg font-semibold text-white mb-4">Статус аккаунта</h3>
    <div class="space-y-3">
      <div class="flex justify-between items-center">
        <span class="text-gray-300">ID пользователя:</span>
        <span class="text-white font-medium">{{ userProfile?.id }}</span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-gray-300">Статус:</span>
        <span [ngClass]="userProfile?.is_staff ? 'text-green-400' : 'text-blue-400'" class="font-medium">
          {{ userProfile?.is_staff ? 'Администратор' : 'Пользователь' }}
        </span>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex flex-col sm:flex-row gap-4">
    <button
      (click)="verifyToken()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
    >
      Проверить токен
    </button>
    <button
      (click)="onRefreshProfile()"
      class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-medium"
    >
      Обновить профиль
    </button>
    <button
      (click)="onLogout()"
      class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors font-medium"
    >
      Выйти
    </button>
  </div>

  </div> <!-- Close profile content div -->
</div>
