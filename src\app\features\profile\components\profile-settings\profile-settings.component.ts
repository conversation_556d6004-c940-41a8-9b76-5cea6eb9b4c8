import { Component, OnInit } from '@angular/core';
import { AuthService, UserProfile } from '../../../../core/services/auth.service';
import { UserService } from '../../../../core/services/user.service';
import { ModalService } from '../../../../core/services/modal.service';

@Component({
  selector: 'app-profile-settings',
  standalone: false,
  templateUrl: './profile-settings.component.html',
  styleUrl: './profile-settings.component.css'
})
export class ProfileSettingsComponent implements OnInit {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.userService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Не удалось загрузить профиль';
        this.isLoading = false;
      }
    });
  }

  onRefreshProfile(): void {
    this.loadUserProfile();
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        this.modalService.success('Успех', 'Токен действителен!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        this.modalService.error('Ошибка', 'Проверка токена не удалась: ' + error.message);
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }
}
